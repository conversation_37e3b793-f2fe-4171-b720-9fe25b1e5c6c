<view class="page">
  <!-- 顶部标签栏 -->
  <view class="tab-bar">
    <view class="tab-container">
      <view wx:for="{{tabs}}" wx:key="key" class="tab-item {{activeTab === item.key ? 'active' : ''}}" data-tab="{{item.key}}" bindtap="onTabChange">
        <text class="tab-text">{{item.name}}（{{item.count}}）</text>
      </view>
    </view>
  </view>

  <!-- 门票列表 -->
  <view class="coupon-list">
    <view wx:if="{{loading && coupons.length === 0}}" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <view wx:elif="{{coupons.length === 0}}" class="empty-container">
      <text class="empty-text">暂无门票</text>
    </view>

    <view wx:else>
      <view wx:for="{{coupons}}" wx:key="id" class="coupon-item" data-coupon-id="{{item.id}}">
        <!-- 门票内容 -->
        <view class="coupon-content">
          <!-- 景区信息 -->
          <view class="scenic-info">
            <view wx:if="{{item.scenicImage}}" class="scenic-image">
              <image src="{{item.scenicImage}}" mode="aspectFill" class="scenic-img"></image>
            </view>
            <view class="scenic-details">
              <text class="scenic-name">{{item.scenicName}}</text>
              <view class="scenic-location-back">
                <text class="scenic-location">{{item.scenicDetail.description}}</text>
              </view>
            </view>
          </view>

          <!-- 门票信息 -->
          <view class="coupon-info">
            <text class="info-item">门票编码：{{item.code}}</text>
            <text class="info-item">购买日期：{{item.formattedValidFrom}}</text>
            <text wx:if="{{item.usedAt}}" class="info-item">使用时间：{{item.formattedUsedAt}}</text>
            <text wx:if="{{item.usedAt}}" class="info-item">到期时间：{{item.formattedValidTo}}</text>
          </view>
        </view>

        <!-- 门票操作 -->
        <view class="coupon-actions">
          <view class="action-left">
            <text class="rules-link" bindtap="viewRules">卡券使用规则</text>
          </view>
          <!-- 组合包使用情况按钮 -->
          <view wx:if="{{item.isBundle}}" class="usage-status-btn" style="background-color: #FF8C00; margin-right: 10px;" data-coupon-id="{{item.id}}" bindtap="viewUsageStatus">
            <text class="usage-status-btn-text">使用情况</text>
          </view>
          <view wx:if="{{item.status === 'unactivated'}}" class="share-btn" style="background-color: #4080FF;" data-coupon-id="{{item.id}}" catchtap="true">
            <text class="use-btn-text">分享卡券</text>
          </view>
          <view class="action-right">
            <view wx:if="{{item.status === 'unactivated' || item.status === 'active'}}" class="use-btn" data-coupon-id="{{item.id}}" catchtap="true">
              <text class="use-btn-text" data-coupon-id="{{item.id}}" bindtap="goToScenicDetail">立即使用</text>
            </view>
            <view wx:elif="{{item.status === 'used'}}" class="used-btn">
              <text class="used-btn-text">已使用</text>
            </view>
            <view wx:elif="{{item.status === 'expired'}}" class="expired-btn">
              <text class="expired-btn-text">已过期</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading && coupons.length > 0}}" class="load-more">
      <text class="load-more-text">加载中...</text>
    </view>

    <view wx:elif="{{!hasMore && coupons.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多门票了</text>
    </view>
  </view>
</view>