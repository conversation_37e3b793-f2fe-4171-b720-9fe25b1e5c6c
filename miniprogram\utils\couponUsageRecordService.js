// 卡券使用记录服务模块
const httpService = require('./httpService');

class CouponUsageRecordService {
  constructor() {
    this.cacheKeyPrefix = 'coupon_usage_records_cache_';
    this.cacheTime = 2 * 60 * 1000; // 2分钟缓存
    console.log('CouponUsageRecordService 初始化成功');
  }

  // 批量创建组合包卡券使用记录
  async createCouponUsageRecords(requestData) {
    try {
      if (!requestData.couponId || !requestData.userId || !requestData.scenicIds) {
        throw new Error('请求参数不完整');
      }

      console.log('批量创建卡券使用记录:', requestData);

      const result = await httpService.post('/coupon-usage-records/batch/bundle', requestData, {
        loadingText: '创建使用记录中...'
      });

      console.log('卡券使用记录创建成功:', result);
      return result;
    } catch (error) {
      console.error('批量创建卡券使用记录失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 根据卡券ID获取使用记录
  async getCouponUsageRecords(couponId) {
    try {
      if (!couponId) {
        throw new Error('卡券ID不能为空');
      }

      console.log('获取卡券使用记录:', couponId);

      const records = await httpService.get(`/api/coupon-usage-records/coupon/${couponId}`, {}, {
        loadingText: '加载使用记录中...'
      });

      console.log('卡券使用记录获取成功:', records);
      return records;
    } catch (error) {
      console.error('获取卡券使用记录失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 标记卡券在指定景区已使用
  async markCouponAsUsed(couponId, scenicId) {
    try {
      if (!couponId || !scenicId) {
        throw new Error('卡券ID和景区ID不能为空');
      }

      console.log('标记卡券已使用:', couponId, scenicId);

      const requestData = {
        couponId: couponId,
        scenicId: scenicId
      };

      const result = await httpService.put('/api/coupon-usage-records/mark-used', requestData, {
        loadingText: '更新使用状态中...'
      });

      console.log('卡券使用状态更新成功:', result);
      return result;
    } catch (error) {
      console.error('标记卡券已使用失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取卡券使用统计
  async getCouponUsageStats(couponId) {
    try {
      if (!couponId) {
        throw new Error('卡券ID不能为空');
      }

      console.log('获取卡券使用统计:', couponId);

      const stats = await httpService.get(`/api/coupon-usage-records/stats/${couponId}`, {}, {
        loadingText: '加载使用统计中...'
      });

      console.log('卡券使用统计获取成功:', stats);
      return stats;
    } catch (error) {
      console.error('获取卡券使用统计失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 检查卡券在指定景区是否已使用
  async checkCouponUsed(couponId, scenicId) {
    try {
      if (!couponId || !scenicId) {
        throw new Error('卡券ID和景区ID不能为空');
      }

      console.log('检查卡券使用状态:', couponId, scenicId);

      const result = await httpService.get('/api/coupon-usage-records/check-used', {
        couponId: couponId,
        scenicId: scenicId
      }, {
        showLoading: false
      });

      console.log('卡券使用状态检查结果:', result);
      return result;
    } catch (error) {
      console.error('检查卡券使用状态失败:', error);
      throw error;
    }
  }

  // 获取卡券未使用的景区列表
  async getUnusedScenicIds(couponId) {
    try {
      if (!couponId) {
        throw new Error('卡券ID不能为空');
      }

      console.log('获取未使用景区列表:', couponId);

      const scenicIds = await httpService.get(`/api/coupon-usage-records/unused-scenics/${couponId}`, {}, {
        showLoading: false
      });

      console.log('未使用景区列表获取成功:', scenicIds);
      return scenicIds;
    } catch (error) {
      console.error('获取未使用景区列表失败:', error);
      throw error;
    }
  }

  // 获取卡券已使用的景区列表
  async getUsedScenicIds(couponId) {
    try {
      if (!couponId) {
        throw new Error('卡券ID不能为空');
      }

      console.log('获取已使用景区列表:', couponId);

      const scenicIds = await httpService.get(`/api/coupon-usage-records/used-scenics/${couponId}`, {}, {
        showLoading: false
      });

      console.log('已使用景区列表获取成功:', scenicIds);
      return scenicIds;
    } catch (error) {
      console.error('获取已使用景区列表失败:', error);
      throw error;
    }
  }

  // 分页查询使用记录
  async getUsageRecordsPage(params = {}) {
    try {
      const {
        current = 1,
        size = 20,
        couponId,
        userId,
        scenicId,
        usageStatus
      } = params;

      const queryParams = {
        current: current.toString(),
        size: size.toString()
      };

      if (couponId) {
        queryParams.couponId = couponId.toString();
      }
      if (userId) {
        queryParams.userId = userId.toString();
      }
      if (scenicId) {
        queryParams.scenicId = scenicId;
      }
      if (usageStatus) {
        queryParams.usageStatus = usageStatus;
      }

      const records = await httpService.get('/api/coupon-usage-records/page', queryParams, {
        loadingText: '加载使用记录中...'
      });

      return records;
    } catch (error) {
      console.error('分页查询使用记录失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 清除缓存
  clearCache() {
    try {
      const keys = wx.getStorageInfoSync().keys;
      keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          wx.removeStorageSync(key);
        }
      });
      console.log('卡券使用记录缓存已清除');
    } catch (error) {
      console.error('清除卡券使用记录缓存失败:', error);
    }
  }
}

// 创建单例实例
const couponUsageRecordService = new CouponUsageRecordService();

module.exports = couponUsageRecordService;
