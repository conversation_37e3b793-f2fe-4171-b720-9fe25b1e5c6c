/* 页面容器 */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签栏 */
.tab-bar {
  background-color: #fff;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  position: relative;
}

.tab-item.active .tab-text {
  color: #007aff;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 30rpx;
  color: #333;
}

/* 门票列表 */
.coupon-list {
  padding: 30rpx 20rpx;
}

.coupon-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 20rpx 20rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}

.coupon-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  background-size: 50rpx 50rpx;
  pointer-events: none;
}

/* 门票头部 */
.coupon-header {
  margin-bottom: 30rpx;
}

.coupon-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.title-text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #000000;
  text-align: left;
  font-style: normal;
  text-transform: none;
  flex: 1;
}

.coupon-price {
  margin-left: 20rpx;
}

.price-text {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

.status-badge {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.status-badge.status-unactivated {
  background-color: rgba(255, 193, 7, 0.9);
  color: #fff;
}

.status-badge.status-active {
  background-color: rgba(40, 167, 69, 0.9);
  color: #fff;
}

.status-badge.status-used {
  background-color: rgba(108, 117, 125, 0.9);
  color: #fff;
}

.status-badge.status-expired {
  background-color: rgba(220, 53, 69, 0.9);
  color: #fff;
}

/* 门票内容 */
.coupon-content {
  margin-bottom: 40rpx;
}

/* 景区信息 */
.scenic-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.scenic-info:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.scenic-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  margin-left: -20rpx;
  flex-shrink: 0;
}

.scenic-img {
  width: 100%;
  height: 100%;
}

.scenic-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.scenic-name {
  font-size: 28rpx;
  color: #000;
  font-weight: 600;
}

.scenic-location-back {
  background-color: rgba(232,243,255,1.000000);
  display: flex;
  width: fit-content; /* 让容器宽度适应内容 */
  padding: 8rpx 10rpx; /* 可选：添加一些内边距 */
}
 
.scenic-location {
  font-size: 24rpx;
  color: rgba(64,128,255,1.000000);
  white-space: nowrap; /* 可选：防止文字换行 */
}

.scenic-arrow {
  margin-left: 20rpx;
  flex-shrink: 0;
}

.arrow-text {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.4);
  font-weight: bold;
}

.coupon-desc {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.9);
  margin-bottom: 20rpx;
  display: block;
}

.coupon-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-item {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.8);
}

/* 门票操作 */
.coupon-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.action-left {
  flex: 1;
}

.rules-link {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.8);
  text-decoration: underline;
}

.action-right {
  flex-shrink: 0;
}

.usage-status-btn {
  background-image: linear-gradient(91deg, #fda02e 0, rgb(230, 144, 47) 100.000000%);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
  align-items: center;
  justify-content: center;
}

.usage-status-btn-text {
  color: rgba(255,255,255,1.000000);
  font-size: 26rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 26rpx;
}

.share-btn {
  background-image: linear-gradient(91deg, rgb(114, 155, 245) 0, rgb(45, 172, 231) 100.000000%);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
  margin-right: 20rpx;
  align-items: center;
  justify-content: center;
}


.use-btn {
  background-image: linear-gradient(91deg, rgba(238,116,53,1.000000) 0, rgba(236,87,91,1.000000) 100.000000%);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
  align-items: center;
  justify-content: center;
}

.use-btn-text {
  width: 112rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}

.used-btn {
  background-color: rgba(108, 117, 125, 0.3);
  border-radius: 25rpx;
  padding: 16rpx 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.used-btn-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

.expired-btn {
  background-color: rgba(220, 53, 69, 0.3);
  border-radius: 25rpx;
  padding: 16rpx 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.expired-btn-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 加载状态 */
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}

.load-more,
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  margin-top: 20rpx;
}

.load-more-text,
.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .coupon-item {
    padding: 30rpx 20rpx;
  }
  
  .title-text {
    font-size: 32rpx;
  }
  
  .coupon-desc {
    font-size: 26rpx;
  }
}
