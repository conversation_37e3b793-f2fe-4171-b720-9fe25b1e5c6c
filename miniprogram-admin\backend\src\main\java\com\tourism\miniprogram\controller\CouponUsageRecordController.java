package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.CouponUsageRecord;
import com.tourism.miniprogram.service.CouponUsageRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 卡券使用记录控制器
 *
 * <AUTHOR> Team
 * @since 2025-06-19
 */
@Slf4j
@RestController
@RequestMapping("/api/coupon-usage-records")
@Api(tags = "卡券使用记录管理")
public class CouponUsageRecordController {

    @Autowired
    private CouponUsageRecordService couponUsageRecordService;

    /**
     * 分页查询卡券使用记录
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询卡券使用记录", notes = "支持按卡券ID、用户ID、景区ID、使用状态等条件查询")
    public Result<IPage<CouponUsageRecord>> getRecordsPage(
            @ApiParam(value = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam(value = "卡券ID") @RequestParam(required = false) Integer couponId,
            @ApiParam(value = "用户ID") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "景区ID") @RequestParam(required = false) String scenicId,
            @ApiParam(value = "使用状态") @RequestParam(required = false) String usageStatus) {
        try {
            Page<CouponUsageRecord> page = new Page<>(current, size);
            QueryWrapper<CouponUsageRecord> queryWrapper = new QueryWrapper<>();

            if (couponId != null) {
                queryWrapper.eq("coupon_id", couponId);
            }
            if (userId != null) {
                queryWrapper.eq("user_id", userId);
            }
            if (StringUtils.hasText(scenicId)) {
                queryWrapper.eq("scenic_id", scenicId);
            }
            if (StringUtils.hasText(usageStatus)) {
                queryWrapper.eq("usage_status", usageStatus);
            }

            queryWrapper.orderByDesc("created_at");
            IPage<CouponUsageRecord> result = couponUsageRecordService.page(page, queryWrapper);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询卡券使用记录失败", e);
            return Result.error("查询失败");
        }
    }

    /**
     * 根据ID获取卡券使用记录详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取卡券使用记录详情", notes = "根据记录ID获取详细信息")
    public Result<CouponUsageRecord> getRecordById(@PathVariable Integer id) {
        try {
            CouponUsageRecord record = couponUsageRecordService.getById(id);
            if (record != null) {
                return Result.success(record);
            } else {
                return Result.error("记录不存在");
            }
        } catch (Exception e) {
            log.error("获取卡券使用记录详情失败，id: {}", id, e);
            return Result.error("获取记录详情失败");
        }
    }

    /**
     * 根据卡券ID获取使用记录列表
     */
    @GetMapping("/coupon/{couponId}")
    @ApiOperation(value = "根据卡券ID获取使用记录", notes = "获取指定卡券的所有使用记录")
    public Result<List<CouponUsageRecord>> getRecordsByCouponId(@PathVariable Integer couponId) {
        try {
            List<CouponUsageRecord> records = couponUsageRecordService.getRecordsByCouponId(couponId);
            return Result.success(records);
        } catch (Exception e) {
            log.error("根据卡券ID获取使用记录失败，couponId: {}", couponId, e);
            return Result.error("获取使用记录失败");
        }
    }

    /**
     * 根据用户ID获取使用记录列表
     */
    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户ID获取使用记录", notes = "获取指定用户的所有使用记录")
    public Result<List<CouponUsageRecord>> getRecordsByUserId(@PathVariable Integer userId) {
        try {
            List<CouponUsageRecord> records = couponUsageRecordService.getRecordsByUserId(userId);
            return Result.success(records);
        } catch (Exception e) {
            log.error("根据用户ID获取使用记录失败，userId: {}", userId, e);
            return Result.error("获取使用记录失败");
        }
    }

    /**
     * 根据景区ID获取使用记录列表
     */
    @GetMapping("/scenic/{scenicId}")
    @ApiOperation(value = "根据景区ID获取使用记录", notes = "获取指定景区的所有使用记录")
    public Result<List<CouponUsageRecord>> getRecordsByScenicId(@PathVariable String scenicId) {
        try {
            List<CouponUsageRecord> records = couponUsageRecordService.getRecordsByScenicId(scenicId);
            return Result.success(records);
        } catch (Exception e) {
            log.error("根据景区ID获取使用记录失败，scenicId: {}", scenicId, e);
            return Result.error("获取使用记录失败");
        }
    }

    /**
     * 获取卡券使用统计
     */
    @GetMapping("/stats/{couponId}")
    @ApiOperation(value = "获取卡券使用统计", notes = "获取指定卡券在各景区的使用统计")
    public Result<List<CouponUsageRecord>> getUsageStats(@PathVariable Integer couponId) {
        try {
            List<CouponUsageRecord> stats = couponUsageRecordService.getUsageStatsByCouponId(couponId);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取卡券使用统计失败，couponId: {}", couponId, e);
            return Result.error("获取使用统计失败");
        }
    }

    /**
     * 创建卡券使用记录
     */
    @PostMapping
    @ApiOperation(value = "创建卡券使用记录", notes = "创建新的卡券使用记录")
    public Result<CouponUsageRecord> createRecord(@RequestBody @Valid CouponUsageRecord record) {
        try {
            boolean success = couponUsageRecordService.save(record);
            if (success) {
                CouponUsageRecord createdRecord = couponUsageRecordService.getById(record.getId());
                return Result.success(createdRecord);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建卡券使用记录失败", e);
            return Result.error("创建记录失败");
        }
    }

    /**
     * 批量创建组合包卡券使用记录
     */
    @PostMapping("/batch/bundle")
    @ApiOperation(value = "批量创建组合包使用记录", notes = "为组合包卡券创建多个景区的使用记录")
    public Result<String> createBundleRecords(@RequestBody Map<String, Object> params) {
        try {
            Integer couponId = (Integer) params.get("couponId");
            Integer userId = (Integer) params.get("userId");
            @SuppressWarnings("unchecked")
            List<String> scenicIds = (List<String>) params.get("scenicIds");

            if (couponId == null || userId == null || scenicIds == null || scenicIds.isEmpty()) {
                return Result.error("参数不完整");
            }

            boolean success = couponUsageRecordService.createBundleUsageRecords(couponId, userId, scenicIds);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("批量创建组合包使用记录失败", e);
            return Result.error("创建记录失败");
        }
    }

    /**
     * 标记卡券在指定景区已使用
     */
    @PutMapping("/mark-used")
    @ApiOperation(value = "标记卡券已使用", notes = "标记卡券在指定景区已使用")
    public Result<String> markAsUsed(@RequestBody Map<String, Object> params) {
        try {
            Integer couponId = (Integer) params.get("couponId");
            String scenicId = (String) params.get("scenicId");

            if (couponId == null || !StringUtils.hasText(scenicId)) {
                return Result.error("参数不完整");
            }

            boolean success = couponUsageRecordService.markAsUsed(couponId, scenicId);
            if (success) {
                return Result.success("标记成功");
            } else {
                return Result.error("标记失败");
            }
        } catch (Exception e) {
            log.error("标记卡券已使用失败", e);
            return Result.error("标记失败");
        }
    }

    /**
     * 检查卡券在指定景区是否已使用
     */
    @GetMapping("/check-used")
    @ApiOperation(value = "检查卡券使用状态", notes = "检查卡券在指定景区是否已使用")
    public Result<Boolean> checkUsed(@RequestParam Integer couponId, @RequestParam String scenicId) {
        try {
            boolean isUsed = couponUsageRecordService.isUsedInScenic(couponId, scenicId);
            return Result.success(isUsed);
        } catch (Exception e) {
            log.error("检查卡券使用状态失败，couponId: {}, scenicId: {}", couponId, scenicId, e);
            return Result.error("检查失败");
        }
    }

    /**
     * 获取卡券未使用的景区列表
     */
    @GetMapping("/unused-scenics/{couponId}")
    @ApiOperation(value = "获取未使用景区列表", notes = "获取卡券未使用的景区ID列表")
    public Result<List<String>> getUnusedScenicIds(@PathVariable Integer couponId) {
        try {
            List<String> scenicIds = couponUsageRecordService.getUnusedScenicIds(couponId);
            return Result.success(scenicIds);
        } catch (Exception e) {
            log.error("获取未使用景区列表失败，couponId: {}", couponId, e);
            return Result.error("获取失败");
        }
    }

    /**
     * 获取卡券已使用的景区列表
     */
    @GetMapping("/used-scenics/{couponId}")
    @ApiOperation(value = "获取已使用景区列表", notes = "获取卡券已使用的景区ID列表")
    public Result<List<String>> getUsedScenicIds(@PathVariable Integer couponId) {
        try {
            List<String> scenicIds = couponUsageRecordService.getUsedScenicIds(couponId);
            return Result.success(scenicIds);
        } catch (Exception e) {
            log.error("获取已使用景区列表失败，couponId: {}", couponId, e);
            return Result.error("获取失败");
        }
    }

    /**
     * 删除卡券使用记录
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除卡券使用记录", notes = "根据ID删除卡券使用记录")
    public Result<String> deleteRecord(@PathVariable Integer id) {
        try {
            boolean success = couponUsageRecordService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除卡券使用记录失败，id: {}", id, e);
            return Result.error("删除失败");
        }
    }
}
