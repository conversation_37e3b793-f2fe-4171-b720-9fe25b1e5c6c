const apiService = require('../../utils/apiService.js');
const couponService = require('../../utils/couponService.js');
const userService = require('../../utils/userService.js');

Page({
  data: {
    scenicId: '',
    province: '',
    title: '',
    productId: '', // 讲解产品ID
    productDetail: null, // 讲解产品详情数据
    lecturerDetail: null, // 讲师详情数据

    // 新的三层级数据结构
    areaList: [], // 区域列表（替代categoryTags）
    selectedArea: null, // 当前选中的区域（替代selectedCategory）
    pointDataList: [], // 讲解点数据列表（包含音频信息）

    // 保持兼容性的字段
    scenicInfo: null, // 景区信息（保持兼容性）
    loading: true, // 加载状态
    error: false, // 错误状态
    errorMessage: '', // 错误信息
    showBackToTop: false, // 是否显示回到顶部按钮

    // 音频播放相关状态
    currentAudio: null, // 当前播放的音频对象
    currentPlayingPointIndex: -1, // 当前播放的讲解点索引
    currentPlayingAudioIndex: -1, // 当前播放的音频索引
    audioContext: null, // 音频上下文
    audio_height: 1500,

    // 当前音频数据和索引（用于布局显示）
    currentAudioData: {
      title: 'A1-大厅',
      duration: '00:02:36',
      location: '大厅',
      descriptionImageUrl: '',
      pointImage: '',
      audioUrl: '',
      id: '',
      isPlaying: false,
      currentTime: '00:00:00',
      progress: 0
    },
    currentAudioIndex: 0,

    // 门票状态相关
    hasActivatedCoupons: false, // 是否有已激活门票
    hasUnactivatedCoupons: false, // 是否有未激活门票
    showBuyButton: true, // 是否显示购买按钮
    canPlayFullAudio: false, // 是否可以播放完整音频
    unactivatedCouponsList: [], // 未激活门票列表
    showCouponModal: false, // 是否显示门票选择弹窗
    currentUserId: null // 当前用户ID
  },

  // 页面加载时获取参数
  onLoad: function (options) {
    console.log('景区详情页面加载');
    console.log('接收到的页面参数:', options);

    this.setData({
      title: decodeURIComponent(options.title || ''),
      productId: options.productId || ''
    });

    // 加载讲解产品详情 
    this.loadProductDetail();
  },

  // 页面显示时
  onShow: function () {
  },

  // 初始化用户信息
  async initUser() {
    try {
      // 获取用户信息
      const userInfo = await userService.getUserInfo();
      console.log('获取到用户信息:', userInfo);

      this.setData({
        currentUserId: userInfo.id
      });

      this.checkCouponStatus();
    } catch (error) {
      console.error('初始化用户信息和门票状态失败:', error);
    }
  },

  // 检查门票状态
  async checkCouponStatus() {
    console.log(this.data.currentUserId, this.data.scenicId)
    try {
      console.log(this.data.currentUserId, this.data.scenicId)
      if (!this.data.currentUserId || !this.data.scenicId) {
        console.log(this.data.currentUserId, this.data.scenicId)
        console.log('用户ID或景区ID为空，跳过门票状态检查');
        return;
      }

      console.log('检查门票状态，用户ID:', this.data.currentUserId, '景区ID:', this.data.scenicId);

      // 并行检查已激活和未激活门票
      const [hasActivated, hasUnactivated] = await Promise.all([
        couponService.hasActivatedCoupons(this.data.currentUserId, this.data.scenicId),
        couponService.hasUnactivatedCoupons(this.data.currentUserId, this.data.scenicId)
      ]);

      console.log('门票状态检查结果 - 已激活:', hasActivated, '未激活:', hasUnactivated);

      // 根据门票状态设置页面状态
      const showBuyButton = !hasActivated; // 有已激活门票就不显示购买按钮
      const canPlayFullAudio = hasActivated; // 有已激活门票就可以播放完整音频

      this.setData({
        hasActivatedCoupons: hasActivated,
        hasUnactivatedCoupons: hasUnactivated,
        showBuyButton: showBuyButton,
        canPlayFullAudio: canPlayFullAudio
      });

      console.log('页面状态更新完成 - 显示购买按钮:', showBuyButton, '可播放完整音频:', canPlayFullAudio);

    } catch (error) {
      console.error('检查门票状态失败:', error);
      // 出错时使用默认状态
      this.setData({
        hasActivatedCoupons: false,
        hasUnactivatedCoupons: false,
        showBuyButton: true,
        canPlayFullAudio: false
      });
    }
  },

  // 页面隐藏时
  onHide: function () {
    console.log('景区详情页面隐藏');
  },

  // 页面滚动监听
  onPageScroll: function (e) {
    const scrollTop = e.scrollTop;
    const showBackToTop = scrollTop > 300; // 滚动超过300px显示回到顶部按钮

    if (this.data.showBackToTop !== showBackToTop) {
      this.setData({
        showBackToTop: showBackToTop
      });
    }
  },

  // 加载讲解产品详情
  async loadProductDetail() {
    try {
      if (!this.data.productId) {
        throw new Error('产品ID不能为空');
      }

      this.setData({
        loading: true,
        error: false
      });

      console.log('开始加载讲解产品详情:', this.data.productId);

      const productDetail = await apiService.getGuideProductDetail(this.data.productId);

      console.log('讲解产品详情加载成功:', productDetail);

      // 处理背景颜色格式
      const formattedBackgroundColor = this.formatBackgroundColor(productDetail.backgroundColor);
      console.log('原始背景颜色:', productDetail.backgroundColor);
      console.log('格式化后背景颜色:', formattedBackgroundColor);

      const formattedProductDetail = {
        ...productDetail,
        backgroundColor: formattedBackgroundColor
      };

      this.setData({
        productDetail: formattedProductDetail,
        loading: false,
        error: false,
        scenicId: formattedProductDetail.scenicId
      });

      // 初始化用户信息和门票状态
      this.initUser();

      // 加载讲师详情
      if (productDetail.lecturerId) {
        this.loadLecturerDetail(productDetail.lecturerId);
      }

      // 加载新的三层级数据
      this.loadHierarchyData();

    } catch (error) {
      console.error('加载讲解产品详情失败:', error);

      this.setData({
        loading: false,
        error: true,
        errorMessage: error.message || '加载失败，请重试'
      });

      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 加载讲师详情
  async loadLecturerDetail(lecturerId) {
    try {
      if (!lecturerId) {
        console.log('讲师ID为空，跳过加载讲师详情');
        return;
      }

      console.log('开始加载讲师详情:', lecturerId);
      const lecturerDetail = await apiService.getLecturerDetail(lecturerId);

      console.log('讲师详情加载成功:', lecturerDetail);
      this.setData({
        lecturerDetail: lecturerDetail
      });

    } catch (error) {
      console.error('加载讲师详情失败:', error);
      // 讲师详情加载失败不影响主流程，只记录错误
    }
  },

  // 加载新的三层级数据（产品-区域-讲解点-音频）
  async loadHierarchyData() {
    try {
      if (!this.data.productId) {
        console.log('产品ID为空，跳过加载三层级数据');
        return;
      }

      console.log('开始加载三层级数据:', this.data.productId);
      const hierarchyData = await apiService.getCompleteHierarchyData(this.data.productId);

      console.log('三层级数据加载成功:', hierarchyData);

      // 格式化讲解点数据用于显示
      const formattedPointData = this.formatAudioDataForDisplay(hierarchyData.audioData);

      this.setData({
        areaList: hierarchyData.areas || [],
        selectedArea: hierarchyData.selectedArea,
        pointDataList: formattedPointData
      });

      // 设置第一个音频为当前显示的音频（如果有的话）
      if (formattedPointData.length > 0 && formattedPointData[0].audioList.length > 0) {
        this.updateCurrentAudioData(0, 0); // 第一个讲解点的第一个音频
      }

      console.log('三层级数据设置完成');

    } catch (error) {
      console.error('加载三层级数据失败:', error);
      // 数据加载失败不影响主流程，只记录错误
      this.setData({
        areaList: [],
        selectedArea: null,
        pointDataList: []
      });
    }
  },

  // 重新加载数据
  onRetryLoad: function () {
    if (this.data.productId) {
      this.loadProductDetail();
    } else if (this.data.scenicId) {
      this.loadScenicInfo();
    }
  },

  // 返回上一页
  onGoBack: function () {
    wx.navigateBack();
  },



  // 回到顶部
  onBackToTop: function () {
    wx.pageScrollTo({
      scrollTop: 1250,
      duration: 300
    });
  },

  // 手动创建指南点分组
  createManualGrouping: function (guidePointList) {
    console.log('开始手动创建分组，数据数量:', guidePointList ? guidePointList.length : 0);

    if (!Array.isArray(guidePointList) || guidePointList.length === 0) {
      console.log('没有有效的指南点数据');
      return [];
    }

    const grouped = {};

    guidePointList.forEach((point, index) => {
      // 检查数据是否有效
      if (!point || typeof point !== 'object') {
        console.log(`第${index + 1}个指南点数据无效:`, point);
        return;
      }

      // 简化的数据格式化，支持多种字段名格式
      const formattedPoint = {
        id: point.id || point.pointId || point.point_id || `point_${index + 1}`,
        pointId: point.point_id || point.pointId || point.id || `point_${index + 1}`,
        title: point.title || point.name || point.pointName || `指南点${index + 1}`,
        location: point.location || point.position || point.place || '',
        audioUrl: point.audio_url || point.audioUrl || point.audio || point.audioPath || '',
        duration: parseInt(point.duration || point.time || point.audioTime || 0),
        exampleImageUrl: point.example_image_url || point.exampleImageUrl || point.exampleImage || point.imageUrl || '',
        descriptionImageUrl: point.description_image_url || point.descriptionImageUrl || point.descriptionImage || point.descImage || '',
        categoryTag: point.category_tag || point.categoryTag || point.category || point.tag || '',
        productId: point.product_id || point.productId || point.guideProductId || this.data.productId || '',
        status: point.status || 1,
        sort: parseInt(point.sort || point.order || point.sequence || index)
      };

      // 使用示例图片URL作为分组键，如果没有则使用默认分组
      const groupKey = formattedPoint.exampleImageUrl || 'default';

      if (!grouped[groupKey]) {
        grouped[groupKey] = {
          exampleImageUrl: groupKey === 'default' ? '' : groupKey,
          points: []
        };
      }

      grouped[groupKey].points.push(formattedPoint);
    });

    // 转换为数组格式，并按排序字段排序
    const result = Object.values(grouped).map(group => ({
      ...group,
      points: group.points.sort((a, b) => (a.sort || 0) - (b.sort || 0))
    }));

    console.log(`分组完成，共${result.length}个分组，总计${result.reduce((sum, group) => sum + group.points.length, 0)}个指南点`);

    // 输出每个分组的信息
    result.forEach((group, index) => {
      console.log(`分组${index + 1}: ${group.exampleImageUrl || '默认分组'}, 包含${group.points.length}个指南点`);
    });

    return result;
  },

  // 格式化音频数据用于显示 - 按讲解点分组
  formatAudioDataForDisplay: function (audioData) {
    if (!Array.isArray(audioData)) {
      return [];
    }

    const formattedData = [];

    audioData.forEach(item => {
      const {
        point,
        audioList
      } = item;

      // 格式化讲解点信息
      const formattedPoint = {
        pointId: point.pointId || point.id,
        pointName: point.pointName || point.name || '未知讲解点',
        pointImage: point.pointImage || point.image || '',
        pointDescription: point.pointDescription || point.description || '',
        sortOrder: point.sortOrder || 0,
        audioList: []
      };

      // 格式化该讲解点下的音频列表
      if (audioList && audioList.length > 0) {
        formattedPoint.audioList = audioList.map((audio, index) => ({
          // 音频基本信息
          id: audio.audioId || audio.id || `audio_${index}`,
          audioId: audio.audioId || audio.id,
          title: audio.audioName || audio.name || '未知音频',
          duration: this.formatTime(audio.duration || 0),
          location: audio.audioAddress || audio.address || '',
          descriptionImageUrl: audio.audioImage || audio.image || '',
          audioUrl: audio.audioUrl || audio.url || '',
          sortOrder: audio.sortOrder || 0,

          // 播放状态字段
          isPlaying: false,
          currentTime: '00:00:00',
          progress: 0,

          // 关联的讲解点信息
          pointId: formattedPoint.pointId,
          pointName: formattedPoint.pointName
        }));

        // 按sortOrder排序音频列表
        formattedPoint.audioList.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
      }

      formattedData.push(formattedPoint);
    });

    // 按sortOrder排序讲解点
    formattedData.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

    console.log('格式化后的讲解点数据:', formattedData);
    return formattedData;
  },

  // 区域标签点击事件（替代原来的分类标签点击）
  onAreaTagClick: function (e) {
    const areaId = e.currentTarget.dataset.areaid;
    const areaName = e.currentTarget.dataset.areaname;
    console.log('点击区域标签:', areaId, areaName);

    // 找到选中的区域对象
    const selectedArea = this.data.areaList.find(area => area.areaId === areaId);
    if (!selectedArea) {
      console.error('未找到选中的区域');
      return;
    }

    // 更新选中的区域
    this.setData({
      selectedArea: selectedArea
    });

    // 加载该区域的音频数据
    this.loadAreaAudioData(areaId);
  },

  // 加载指定区域的音频数据
  async loadAreaAudioData(areaId) {
    try {
      console.log('加载区域音频数据:', areaId);

      wx.showLoading({
        title: '加载中...'
      });

      const audioData = await apiService.getAreaAudioData(areaId);
      const formattedPointData = this.formatAudioDataForDisplay(audioData);

      this.setData({
        pointDataList: formattedPointData
      });

      wx.hideLoading();
      console.log('区域音频数据加载完成');

    } catch (error) {
      console.error('加载区域音频数据失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 音频播放事件 - 支持播放/暂停切换
  onAudioPlay: function (e) {
    const audioUrl = e.currentTarget.dataset.audiourl;
    const audioTitle = e.currentTarget.dataset.audiotitle;
    const audioId = e.currentTarget.dataset.audioid;
    const pointIndex = parseInt(e.currentTarget.dataset.pointindex);
    const audioIndex = parseInt(e.currentTarget.dataset.audioindex);

    console.log('点击音频:', audioTitle, audioUrl, '讲解点索引:', pointIndex, '音频索引:', audioIndex);
    console.log('当前门票状态 - 已激活:', this.data.hasActivatedCoupons, '未激活:', this.data.hasUnactivatedCoupons);

    if (!audioUrl) {
      wx.showToast({
        title: '音频地址无效',
        icon: 'none'
      });
      return;
    }

    // 检查是否有已激活门票，如果没有则需要处理门票逻辑
    if (!this.data.hasActivatedCoupons) {
      this.handleAudioPlayWithoutActivatedCoupon(audioUrl, audioTitle, audioId, pointIndex, audioIndex);
      return;
    }

    // 如果点击的是当前正在播放的音频
    if (this.data.currentPlayingPointIndex === pointIndex &&
      this.data.currentPlayingAudioIndex === audioIndex &&
      this.data.audioContext) {

      // 检查音频当前状态
      if (this.data.audioContext.paused) {
        // 如果音频已暂停，则继续播放
        this.resumeAudio();
      } else {
        // 如果音频正在播放，则暂停
        this.pauseAudio();
      }
      return;
    }

    // 如果有其他音频在播放，先暂停之前的音频
    if (this.data.audioContext) {
      console.log('检测到其他音频正在播放，先暂停之前的音频');
      this.stopAudio();
    }

    // 开始播放新音频（完整版本）
    this.playAudio(audioUrl, audioTitle, audioId, pointIndex, audioIndex, false);

    // 更新当前音频数据显示
    this.updateCurrentAudioData(pointIndex, audioIndex);
  },

  // 处理没有已激活门票时的音频播放
  async handleAudioPlayWithoutActivatedCoupon(audioUrl, audioTitle, audioId, pointIndex, audioIndex) {
    console.log('用户没有已激活门票，检查未激活门票状态');

    // 如果有未激活门票，显示激活选择弹窗
    if (this.data.hasUnactivatedCoupons) {
      await this.showCouponActivationModal();
      return;
    }

    // 如果没有任何门票，播放试听版本（前3秒）
    console.log('用户没有任何门票，播放试听版本');
    wx.showToast({
      title: '试听模式：仅播放前3秒',
      icon: 'none',
      duration: 2000
    });

    // 如果有其他音频在播放，先暂停之前的音频
    if (this.data.audioContext) {
      this.stopAudio();
    }

    // 播放试听版本（限制3秒）
    this.playAudio(audioUrl, audioTitle, audioId, pointIndex, audioIndex, true);

    // 更新当前音频数据显示
    this.updateCurrentAudioData(pointIndex, audioIndex);
  },

  // 显示门票激活确认弹窗
  async showCouponActivationModal() {
    try {
      console.log('检测到未激活门票，显示激活确认弹窗');

      // 获取未激活门票列表
      const unactivatedCoupons = await couponService.getUnactivatedCouponsByScenic(
        this.data.currentUserId,
        this.data.scenicId
      );

      console.log('获取到未激活门票列表:', unactivatedCoupons);

      if (!unactivatedCoupons || unactivatedCoupons.length === 0) {
        wx.showToast({
          title: '没有可激活的门票',
          icon: 'none'
        });
        return;
      }

      // 按创建时间排序，获取最旧的门票（最早购买的）
      const sortedCoupons = unactivatedCoupons.sort((a, b) => {
        const timeA = new Date(a.createdAt || a.createTime || 0).getTime();
        const timeB = new Date(b.createdAt || b.createTime || 0).getTime();
        return timeA - timeB; // 升序排列，最旧的在前面
      });

      const oldestCoupon = sortedCoupons[0];
      console.log('选择最旧的门票进行激活:', oldestCoupon);

      // 显示确认激活弹窗
      wx.showModal({
        title: '激活门票',
        content: '您有未激活的卡券，是否确认激活？',
        confirmText: '确认激活',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            // 用户确认激活，激活最旧的门票
            await this.activateOldestCoupon(oldestCoupon);
          } else {
            console.log('用户取消激活');
          }
        }
      });

    } catch (error) {
      console.error('获取未激活门票列表失败:', error);
      wx.showToast({
        title: '获取门票信息失败',
        icon: 'none'
      });
    }
  },

  // 激活最旧的门票
  async activateOldestCoupon(coupon) {
    try {
      console.log('激活最旧的门票:', coupon);

      wx.showLoading({
        title: '激活中...'
      });

      // 调用激活接口
      await couponService.activateCoupon(coupon.id);

      wx.hideLoading();

      wx.showToast({
        title: '激活成功！',
        icon: 'success',
        duration: 2000
      });

      // 重新检查门票状态
      await this.checkCouponStatus();

      // 延迟显示提示
      setTimeout(() => {
        wx.showToast({
          title: '现在可以播放完整音频了',
          icon: 'none',
          duration: 2000
        });
      }, 2000);

    } catch (error) {
      wx.hideLoading();
      console.error('激活门票失败:', error);
      wx.showToast({
        title: '激活失败，请重试',
        icon: 'none'
      });
    }
  },

  // 格式化门票时间显示
  formatCouponTime(timeString) {
    if (!timeString) return '未知时间';

    try {
      const date = new Date(timeString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return '时间格式错误';
    }
  },

  // 播放音频
  playAudio: function (audioUrl, audioTitle, audioId, pointIndex, audioIndex, isTrialMode = false) {
    console.log('开始播放音频:', audioTitle, '试听模式:', isTrialMode);

    // 创建音频上下文实例
    const audioContext = wx.createInnerAudioContext();
    audioContext.src = audioUrl;

    // 更新播放状态
    this.setData({
      audioContext: audioContext,
      currentPlayingPointIndex: pointIndex,
      currentPlayingAudioIndex: audioIndex
    });

    // 更新音频列表中的播放状态
    this.updateAudioPlayingState(pointIndex, audioIndex, true);

    // 试听模式的定时器
    let trialTimer = null;

    // 监听音频开始播放事件
    audioContext.onPlay(() => {
      console.log('音频开始播放:', audioTitle);
      const toastTitle = isTrialMode ? '试听播放（3秒）' : '开始播放';
      wx.showToast({
        title: toastTitle,
        icon: 'none',
        duration: 1000
      });

      // 如果是试听模式，设置3秒后自动停止
      if (isTrialMode) {
        trialTimer = setTimeout(() => {
          console.log('试听时间结束，停止播放');
          this.stopAudio();
          wx.showToast({
            title: '试听结束，请购买门票体验完整内容',
            icon: 'none',
            duration: 3000
          });
        }, 3000); // 3秒后停止
      }
    });

    // 监听音频播放进度
    audioContext.onTimeUpdate(() => {
      const currentTime = audioContext.currentTime;
      const duration = audioContext.duration;
      const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

      // 试听模式下，如果超过3秒就停止
      if (isTrialMode && currentTime >= 3) {
        this.stopAudio();
        return;
      }

      // 更新进度显示
      this.updateAudioProgress(pointIndex, audioIndex, currentTime, progress);
    });

    // 监听音频播放结束事件
    audioContext.onEnded(() => {
      console.log('音频播放结束:', audioTitle);
      if (trialTimer) {
        clearTimeout(trialTimer);
        trialTimer = null;
      }
      this.stopAudio();
    });

    // 监听音频错误事件
    audioContext.onError((error) => {
      console.error('音频播放出错:', error);
      if (trialTimer) {
        clearTimeout(trialTimer);
        trialTimer = null;
      }
      wx.showToast({
        title: '播放失败',
        icon: 'none'
      });
      this.stopAudio();
    });

    // 开始播放
    audioContext.play();
  },

  // 暂停音频
  pauseAudio: function () {
    if (this.data.audioContext) {
      this.data.audioContext.pause();
      this.updateAudioPlayingState(this.data.currentPlayingPointIndex, this.data.currentPlayingAudioIndex, false);

      wx.showToast({
        title: '已暂停',
        icon: 'none',
        duration: 1000
      });
    }
  },

  // 恢复播放音频
  resumeAudio: function () {
    if (this.data.audioContext) {
      this.data.audioContext.play();
      this.updateAudioPlayingState(this.data.currentPlayingPointIndex, this.data.currentPlayingAudioIndex, true);

      wx.showToast({
        title: '继续播放',
        icon: 'none',
        duration: 1000
      });
    }
  },

  // 停止音频
  stopAudio: function () {
    if (this.data.audioContext) {
      this.data.audioContext.stop();
      this.data.audioContext.destroy();

      // 重置播放状态
      this.updateAudioPlayingState(this.data.currentPlayingPointIndex, this.data.currentPlayingAudioIndex, false);
      this.setData({
        audioContext: null,
        currentPlayingPointIndex: -1,
        currentPlayingAudioIndex: -1
      });
    }
  },

  // 更新音频播放状态
  updateAudioPlayingState: function (pointIndex, audioIndex, isPlaying) {
    if (pointIndex < 0 || pointIndex >= this.data.pointDataList.length) return;
    if (audioIndex < 0 || audioIndex >= this.data.pointDataList[pointIndex].audioList.length) return;

    const pointDataList = [...this.data.pointDataList];
    pointDataList[pointIndex].audioList[audioIndex] = {
      ...pointDataList[pointIndex].audioList[audioIndex],
      isPlaying: isPlaying
    };

    this.setData({
      pointDataList: pointDataList
    });
  },

  // 更新音频播放进度
  updateAudioProgress: function (pointIndex, audioIndex, currentTime, progress) {
    if (pointIndex < 0 || pointIndex >= this.data.pointDataList.length) return;
    if (audioIndex < 0 || audioIndex >= this.data.pointDataList[pointIndex].audioList.length) return;

    const pointDataList = [...this.data.pointDataList];
    pointDataList[pointIndex].audioList[audioIndex] = {
      ...pointDataList[pointIndex].audioList[audioIndex],
      currentTime: this.formatTime(currentTime),
      progress: progress
    };

    this.setData({
      pointDataList: pointDataList
    });
  },

  // 切换到下一个音频
  switchToNextAudio: function () {
    // 这个方法需要重新实现，因为现在是二维结构
    // 暂时保留，后续可以实现跨讲解点的音频切换
  },

  // 切换到上一个音频
  switchToPrevAudio: function () {
    // 这个方法需要重新实现，因为现在是二维结构
    // 暂时保留，后续可以实现跨讲解点的音频切换
  },

  // 格式化时间显示
  formatTime: function (seconds) {
    if (!seconds || seconds < 0) return '00:00:00';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // 格式化背景颜色
  formatBackgroundColor: function (backgroundColor) {
    if (!backgroundColor) {
      return 'rgba(245, 245, 249, 1.000000)'; // 默认背景色
    }

    // 如果已经是有效的CSS颜色格式，直接返回
    if (backgroundColor.startsWith('rgb') || backgroundColor.startsWith('#') || backgroundColor.startsWith('rgba')) {
      return backgroundColor;
    }

    // 如果是纯数字或其他格式，尝试转换
    if (typeof backgroundColor === 'string') {
      // 移除可能的空格和特殊字符
      const cleanColor = backgroundColor.trim();

      // 如果是十六进制颜色（没有#前缀）
      if (/^[0-9A-Fa-f]{6}$/.test(cleanColor)) {
        return `#${cleanColor}`;
      }

      // 如果是RGB格式的数字（如 "255,255,255"）
      if (/^\d+,\s*\d+,\s*\d+$/.test(cleanColor)) {
        return `rgb(${cleanColor})`;
      }

      // 如果是RGBA格式的数字（如 "255,255,255,1"）
      if (/^\d+,\s*\d+,\s*\d+,\s*[\d.]+$/.test(cleanColor)) {
        return `rgba(${cleanColor})`;
      }
    }

    // 如果无法识别格式，返回默认颜色
    console.warn('无法识别的背景颜色格式:', backgroundColor);
    return 'rgba(245, 245, 249, 1.000000)';
  },

  // 更新当前音频数据显示
  updateCurrentAudioData: function (pointIndex, audioIndex) {
    if (pointIndex < 0 || pointIndex >= this.data.pointDataList.length) return;
    if (audioIndex < 0 || audioIndex >= this.data.pointDataList[pointIndex].audioList.length) return;

    const pointData = this.data.pointDataList[pointIndex];
    const audioData = pointData.audioList[audioIndex];

    this.setData({
      currentAudioData: {
        title: audioData.title || 'A1-大厅',
        duration: audioData.duration || '00:02:36',
        location: audioData.location || '大厅',
        descriptionImageUrl: audioData.descriptionImageUrl || '',
        pointImage: pointData.pointImage || '',
        audioUrl: audioData.audioUrl || '',
        id: audioData.id || '',
        isPlaying: audioData.isPlaying || false,
        currentTime: audioData.currentTime || '00:00:00',
        progress: audioData.progress || 0
      },
      currentAudioIndex: audioIndex,
      currentPointIndex: pointIndex
    });
  },



  // 立即购买（根据门票状态决定是否显示）
  onBuyNow: function () {
    // 如果有已激活门票，不应该显示购买按钮
    if (this.data.hasActivatedCoupons) {
      wx.showToast({
        title: '您已拥有该景区门票',
        icon: 'none'
      });
      return;
    }

    if (!this.data.productDetail) {
      wx.showToast({
        title: '产品信息加载中',
        icon: 'none'
      });
      return;
    }

    if (!this.data.productId) {
      wx.showToast({
        title: '产品ID不能为空',
        icon: 'none'
      });
      return;
    }

    // 只传递讲解产品ID到订单确认页面
    wx.navigateTo({
      url: `/pages/lanhu_querendingdan/component?productId=${this.data.productId}`
    });
  },

  // 页面隐藏时停止音频播放
  onHide: function () {
    console.log('景区详情页面隐藏');
    this.stopAudio();
  },

  // 页面卸载时停止音频播放
  onUnload: function () {
    console.log('景区详情页面卸载');
    this.stopAudio();
  }


});