package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Coupon;
import com.tourism.miniprogram.entity.ActivationCode;
import com.tourism.miniprogram.mapper.CouponMapper;
import com.tourism.miniprogram.service.CouponService;
import com.tourism.miniprogram.service.ActivationCodeService;
import com.tourism.miniprogram.service.UsageRecordService;
import com.tourism.miniprogram.service.ProductService;
import com.tourism.miniprogram.entity.Product;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 数字门票服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class CouponServiceImpl extends ServiceImpl<CouponMapper, Coupon> implements CouponService {

    @Autowired
    private ActivationCodeService activationCodeService;

    @Autowired
    private UsageRecordService usageRecordService;

    @Autowired
    private ProductService productService;

    @Override
    public List<Coupon> getCouponsByUserId(Integer userId) {
        try {
            return baseMapper.selectCouponsByUserId(userId);
        } catch (Exception e) {
            log.error("根据用户ID获取门票列表失败，userId: {}", userId, e);
            throw new RuntimeException("根据用户ID获取门票列表失败");
        }
    }

    @Override
    public List<Coupon> getCouponsByScenicId(String scenicId) {
        try {
            return baseMapper.selectCouponsByScenicId(scenicId);
        } catch (Exception e) {
            log.error("根据景区ID获取门票列表失败，scenicId: {}", scenicId, e);
            throw new RuntimeException("根据景区ID获取门票列表失败");
        }
    }

    @Override
    public List<Coupon> getCouponsByStatus(String status) {
        try {
            return baseMapper.selectCouponsByStatus(status);
        } catch (Exception e) {
            log.error("根据状态获取门票列表失败，status: {}", status, e);
            throw new RuntimeException("根据状态获取门票列表失败");
        }
    }

    @Override
    public List<Coupon> getValidCouponsByUserAndScenic(Integer userId, String scenicId) {
        try {
            return baseMapper.selectValidCouponsByUserAndScenic(userId, scenicId);
        } catch (Exception e) {
            log.error("根据用户ID和景区ID获取有效门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            throw new RuntimeException("根据用户ID和景区ID获取有效门票失败");
        }
    }

    @Override
    @Transactional
    public boolean activateCoupon(Integer couponId, String activationCode) {
        try {
            // 查找门票
            Coupon coupon = getById(couponId);
            if (coupon == null) {
                log.error("门票不存在，couponId: {}", couponId);
                return false;
            }

            // 检查门票状态
            if (!"unactivated".equals(coupon.getStatus())) {
                log.error("门票状态不正确，无法激活，couponId: {}, status: {}", couponId, coupon.getStatus());
                return false;
            }

            // 查找激活码
            ActivationCode activationCodeEntity = activationCodeService.getByCode(activationCode);
            if (activationCodeEntity == null) {
                log.error("激活码不存在，code: {}", activationCode);
                return false;
            }

            // 检查激活码状态
            if (!"unused".equals(activationCodeEntity.getStatus())) {
                log.error("激活码已被使用或无效，code: {}, status: {}", activationCode, activationCodeEntity.getStatus());
                return false;
            }

            // 景区ID验证已移除，因为门票不再关联特定景区

            // 激活门票
            coupon.setStatus("active");
            coupon.setActivationCodeId(activationCodeEntity.getId());
            coupon.setValidFrom(LocalDateTime.now());
            // 设置过期时间（假设激活后24小时有效）
            coupon.setValidTo(LocalDateTime.now().plusHours(24));

            // 更新激活码状态
            activationCodeEntity.setStatus("used");
            activationCodeEntity.setCouponId(couponId);
            activationCodeEntity.setUsedAt(LocalDateTime.now());

            // 保存更新
            boolean couponUpdated = updateById(coupon);
            boolean codeUpdated = activationCodeService.updateById(activationCodeEntity);

            if (couponUpdated && codeUpdated) {
                log.info("门票激活成功，couponId: {}, activationCode: {}", couponId, activationCode);
                return true;
            } else {
                log.error("门票激活失败，数据库更新失败");
                return false;
            }

        } catch (Exception e) {
            log.error("激活门票失败，couponId: {}, activationCode: {}", couponId, activationCode, e);
            throw new RuntimeException("激活门票失败");
        }
    }

    @Override
    @Transactional
    public boolean activateCouponDirect(Integer couponId) {
        try {
            log.info("开始直接激活门票，couponId: {}", couponId);

            // 查找门票
            Coupon coupon = getById(couponId);
            if (coupon == null) {
                log.error("门票不存在，couponId: {}", couponId);
                return false;
            }

            // 检查门票状态
            if (!"unactivated".equals(coupon.getStatus())) {
                log.error("门票状态不正确，无法激活，couponId: {}, status: {}", couponId, coupon.getStatus());
                return false;
            }

            // 激活门票
            LocalDateTime now = LocalDateTime.now();
            coupon.setStatus("active");
            coupon.setUsedAt(now);
            coupon.setValidTo(now.plusHours(12)); // 设置为当前时间加12小时
            coupon.setUpdatedAt(now);

            // 保存更新
            boolean updated = updateById(coupon);

            if (updated) {
                log.info("门票直接激活成功，couponId: {}, validTo: {}", couponId, coupon.getValidTo());
                return true;
            } else {
                log.error("门票直接激活失败，数据库更新失败，couponId: {}", couponId);
                return false;
            }

        } catch (Exception e) {
            log.error("直接激活门票失败，couponId: {}", couponId, e);
            throw new RuntimeException("直接激活门票失败");
        }
    }

    @Override
    @Transactional
    public boolean useCoupon(Integer couponId, Integer userId, String scenicId) {
        try {
            // 查找门票
            Coupon coupon = getById(couponId);
            if (coupon == null) {
                log.error("门票不存在，couponId: {}", couponId);
                return false;
            }

            // 检查门票状态
            if (!"active".equals(coupon.getStatus())) {
                log.error("门票状态不正确，无法使用，couponId: {}, status: {}", couponId, coupon.getStatus());
                return false;
            }

            // 检查门票是否属于该用户
            if (!coupon.getUserId().equals(userId)) {
                log.error("门票不属于该用户，couponId: {}, userId: {}, couponUserId: {}",
                         couponId, userId, coupon.getUserId());
                return false;
            }

            // 景区ID验证已移除，门票现在可以在任何景区使用

            // 检查门票是否过期
            if (coupon.getValidTo() != null && coupon.getValidTo().isBefore(LocalDateTime.now())) {
                log.error("门票已过期，couponId: {}, validTo: {}", couponId, coupon.getValidTo());
                return false;
            }

            // 使用门票
            coupon.setStatus("used");
            coupon.setUsedAt(LocalDateTime.now());

            // 创建使用记录 - 需要修改usageRecordService以支持String类型的scenicId
            boolean recordCreated = usageRecordService.createUsageRecord(couponId, userId, scenicId);
            boolean couponUpdated = updateById(coupon);

            if (couponUpdated && recordCreated) {
                log.info("门票使用成功，couponId: {}, userId: {}, scenicId: {}", couponId, userId, scenicId);
                return true;
            } else {
                log.error("门票使用失败，数据库更新失败");
                return false;
            }

        } catch (Exception e) {
            log.error("使用门票失败，couponId: {}, userId: {}, scenicId: {}", couponId, userId, scenicId, e);
            throw new RuntimeException("使用门票失败");
        }
    }

    @Override
    public String generateCouponCode() {
        // 生成唯一的门票码
        return "CPN" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    @Override
    public List<Coupon> getUnactivatedCouponsByUserAndScenic(Integer userId, String scenicId) {
        try {
            log.info("查询用户未激活门票（支持组合包），userId: {}, scenicId: {}", userId, scenicId);

            // 参数验证
            if (userId == null) {
                log.warn("用户ID为空，返回空列表");
                return new ArrayList<>();
            }
            if (scenicId == null || scenicId.trim().isEmpty()) {
                log.warn("景区ID为空，返回空列表");
                return new ArrayList<>();
            }

            // 先查询该用户的所有未激活门票，用于调试
            List<Coupon> allUserCoupons = baseMapper.selectList(
                new QueryWrapper<Coupon>()
                    .eq("user_id", userId)
                    .eq("status", "unactivated")
            );
            log.info("用户{}的所有未激活门票数量: {}", userId, allUserCoupons.size());
            if (!allUserCoupons.isEmpty()) {
                log.info("用户所有未激活门票详情: {}", allUserCoupons);
            }

            // 执行支持组合包的查询（使用FIND_IN_SET支持逗号分隔的景区ID）
            List<Coupon> coupons = baseMapper.selectUnactivatedCouponsByUserAndScenic(userId, scenicId);
            log.info("通过景区ID查询到的未激活门票数量（包含组合包）: {}", coupons != null ? coupons.size() : 0);

            if (coupons != null && !coupons.isEmpty()) {
                log.info("通过景区ID查询到的未激活门票详情（包含组合包）: {}", coupons);
            } else {
                log.warn("通过景区ID没有查询到未激活门票，可能是JOIN查询问题或景区ID不匹配");
                log.info("注意：查询已支持组合包产品，会检查scenic_id字段中的逗号分隔值");

                // 尝试查询products表，看看是否有匹配的景区ID
                log.info("尝试验证景区ID是否存在于products表中...");

                // 查询用户所有未激活门票对应的产品信息
                if (!allUserCoupons.isEmpty()) {
                    log.info("开始查询用户未激活门票对应的产品信息...");
                    for (Coupon coupon : allUserCoupons) {
                        try {
                            Product product = productService.getById(coupon.getProductId());
                            if (product != null) {
                                log.info("门票ID: {}, 产品ID: {}, 产品名称: {}, 产品景区ID: {}",
                                    coupon.getId(), coupon.getProductId(), product.getName(), product.getScenicId());

                                // 检查产品的景区ID是否匹配
                                if (scenicId.equals(product.getScenicId())) {
                                    log.info("找到匹配的产品！产品ID: {}, 景区ID: {}", product.getId(), product.getScenicId());
                                } else {
                                    log.info("产品景区ID不匹配，期望: {}, 实际: {}", scenicId, product.getScenicId());
                                }
                            } else {
                                log.warn("产品不存在，产品ID: {}", coupon.getProductId());
                            }
                        } catch (Exception e) {
                            log.error("查询产品信息失败，产品ID: {}", coupon.getProductId(), e);
                        }
                    }
                }
            }

            return coupons != null ? coupons : new ArrayList<>();
        } catch (Exception e) {
            log.error("根据用户ID和景区ID查询未激活门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            throw new RuntimeException("查询未激活门票失败");
        }
    }

    @Override
    public boolean hasUnactivatedCoupons(Integer userId, String scenicId) {
        try {
            log.info("检查用户是否有未激活门票（支持组合包），userId: {}, scenicId: {}", userId, scenicId);

            // 参数验证
            if (userId == null) {
                log.warn("用户ID为空，返回false");
                return false;
            }
            if (scenicId == null || scenicId.trim().isEmpty()) {
                log.warn("景区ID为空，返回false");
                return false;
            }

            List<Coupon> coupons = getUnactivatedCouponsByUserAndScenic(userId, scenicId);
            log.info("查询到的未激活门票列表: {}", coupons);

            boolean hasUnactivated = coupons != null && !coupons.isEmpty();
            log.info("用户{}未激活门票检查结果: {}, 门票数量: {}",
                    hasUnactivated ? "有" : "没有",
                    hasUnactivated,
                    coupons != null ? coupons.size() : 0);

            return hasUnactivated;
        } catch (Exception e) {
            log.error("检查用户未激活门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            // 发生异常时返回false，而不是抛出异常
            return false;
        }
    }

    @Override
    public List<Coupon> getUnactivatedCouponsByUserAndProduct(Integer userId, String productId) {
        try {
            log.info("查询用户未激活门票（按产品ID），userId: {}, productId: {}", userId, productId);
            List<Coupon> coupons = baseMapper.selectUnactivatedCouponsByUserAndProduct(userId, productId);
            log.info("查询到未激活门票数量: {}", coupons.size());
            return coupons;
        } catch (Exception e) {
            log.error("根据用户ID和产品ID查询未激活门票失败，userId: {}, productId: {}", userId, productId, e);
            throw new RuntimeException("查询未激活门票失败");
        }
    }

    @Override
    public boolean hasUnactivatedCouponsByProduct(Integer userId, String productId) {
        try {
            log.info("检查用户是否有未激活门票（按产品ID），userId: {}, productId: {}", userId, productId);
            List<Coupon> coupons = getUnactivatedCouponsByUserAndProduct(userId, productId);
            boolean hasUnactivated = !coupons.isEmpty();
            log.info("用户{}激活门票检查结果: {}", hasUnactivated ? "有未" : "没有未", hasUnactivated);
            return hasUnactivated;
        } catch (Exception e) {
            log.error("检查用户未激活门票失败，userId: {}, productId: {}", userId, productId, e);
            throw new RuntimeException("检查未激活门票失败");
        }
    }

    @Override
    public List<Coupon> getActivatedCouponsByUserAndScenic(Integer userId, String scenicId) {
        try {
            log.info("查询用户已激活门票（支持组合包），userId: {}, scenicId: {}", userId, scenicId);
            List<Coupon> coupons = baseMapper.selectActivatedCouponsByUserAndScenic(userId, scenicId);
            log.info("查询到已激活门票数量（包含组合包）: {}", coupons.size());
            return coupons;
        } catch (Exception e) {
            log.error("根据用户ID和景区ID查询已激活门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            throw new RuntimeException("查询已激活门票失败");
        }
    }

    @Override
    public boolean hasActivatedCoupons(Integer userId, String scenicId) {
        try {
            log.info("检查用户是否有已激活门票（支持组合包），userId: {}, scenicId: {}", userId, scenicId);
            List<Coupon> coupons = getActivatedCouponsByUserAndScenic(userId, scenicId);
            boolean hasActivated = !coupons.isEmpty();
            log.info("用户{}已激活门票检查结果: {}", hasActivated ? "有" : "没有", hasActivated);
            return hasActivated;
        } catch (Exception e) {
            log.error("检查用户已激活门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            throw new RuntimeException("检查已激活门票失败");
        }
    }

    @Override
    public List<Coupon> getActivatedCouponsByUserAndProduct(Integer userId, String productId) {
        try {
            log.info("查询用户已激活门票（按产品ID），userId: {}, productId: {}", userId, productId);
            List<Coupon> coupons = baseMapper.selectActivatedCouponsByUserAndProduct(userId, productId);
            log.info("查询到已激活门票数量: {}", coupons.size());
            return coupons;
        } catch (Exception e) {
            log.error("根据用户ID和产品ID查询已激活门票失败，userId: {}, productId: {}", userId, productId, e);
            throw new RuntimeException("查询已激活门票失败");
        }
    }

    @Override
    public boolean hasActivatedCouponsByProduct(Integer userId, String productId) {
        try {
            log.info("检查用户是否有已激活门票（按产品ID），userId: {}, productId: {}", userId, productId);
            List<Coupon> coupons = getActivatedCouponsByUserAndProduct(userId, productId);
            boolean hasActivated = !coupons.isEmpty();
            log.info("用户{}已激活门票检查结果: {}", hasActivated ? "有" : "没有", hasActivated);
            return hasActivated;
        } catch (Exception e) {
            log.error("检查用户已激活门票失败，userId: {}, productId: {}", userId, productId, e);
            throw new RuntimeException("检查已激活门票失败");
        }
    }

    @Override
    @Transactional
    public int processExpiredCoupons() {
        try {
            log.info("开始处理过期卡券...");

            // 查询满足过期条件的卡券
            List<Coupon> expiredCoupons = baseMapper.selectExpiredCoupons();

            if (expiredCoupons == null || expiredCoupons.isEmpty()) {
                log.info("没有找到需要处理的过期卡券");
                return 0;
            }

            log.info("找到{}张过期卡券需要处理", expiredCoupons.size());

            // 提取卡券ID列表
            List<Integer> couponIds = new ArrayList<>();
            for (Coupon coupon : expiredCoupons) {
                couponIds.add(coupon.getId());
                log.debug("过期卡券详情 - ID: {}, Code: {}, UsedAt: {}, ValidTo: {}, Status: {}",
                    coupon.getId(), coupon.getCode(), coupon.getUsedAt(),
                    coupon.getValidTo(), coupon.getStatus());
            }

            // 批量更新卡券状态为'used'
            int updatedCount = baseMapper.batchUpdateExpiredCouponsStatus(couponIds);

            if (updatedCount > 0) {
                log.info("成功处理{}张过期卡券，状态已更新为'used'", updatedCount);
            } else {
                log.warn("过期卡券处理失败，没有记录被更新");
            }

            return updatedCount;

        } catch (Exception e) {
            log.error("处理过期卡券失败", e);
            throw new RuntimeException("处理过期卡券失败: " + e.getMessage());
        }
    }
}
