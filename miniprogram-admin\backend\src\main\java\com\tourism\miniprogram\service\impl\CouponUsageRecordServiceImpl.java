package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.CouponUsageRecord;
import com.tourism.miniprogram.mapper.CouponUsageRecordMapper;
import com.tourism.miniprogram.service.CouponUsageRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡券使用记录服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-06-19
 */
@Slf4j
@Service
public class CouponUsageRecordServiceImpl extends ServiceImpl<CouponUsageRecordMapper, CouponUsageRecord> implements CouponUsageRecordService {

    @Override
    public List<CouponUsageRecord> getRecordsByCouponId(Integer couponId) {
        try {
            return baseMapper.selectRecordsByCouponId(couponId);
        } catch (Exception e) {
            log.error("根据卡券ID获取使用记录失败，couponId: {}", couponId, e);
            throw new RuntimeException("根据卡券ID获取使用记录失败");
        }
    }

    @Override
    public List<CouponUsageRecord> getRecordsByUserId(Integer userId) {
        try {
            return baseMapper.selectRecordsByUserId(userId);
        } catch (Exception e) {
            log.error("根据用户ID获取使用记录失败，userId: {}", userId, e);
            throw new RuntimeException("根据用户ID获取使用记录失败");
        }
    }

    @Override
    public List<CouponUsageRecord> getRecordsByScenicId(String scenicId) {
        try {
            return baseMapper.selectRecordsByScenicId(scenicId);
        } catch (Exception e) {
            log.error("根据景区ID获取使用记录失败，scenicId: {}", scenicId, e);
            throw new RuntimeException("根据景区ID获取使用记录失败");
        }
    }

    @Override
    public CouponUsageRecord getRecordByCouponAndScenic(Integer couponId, String scenicId) {
        try {
            return baseMapper.selectRecordByCouponAndScenic(couponId, scenicId);
        } catch (Exception e) {
            log.error("根据卡券ID和景区ID获取使用记录失败，couponId: {}, scenicId: {}", couponId, scenicId, e);
            throw new RuntimeException("根据卡券ID和景区ID获取使用记录失败");
        }
    }

    @Override
    public List<CouponUsageRecord> getRecordsByUserAndScenic(Integer userId, String scenicId) {
        try {
            return baseMapper.selectRecordsByUserAndScenic(userId, scenicId);
        } catch (Exception e) {
            log.error("根据用户ID和景区ID获取使用记录失败，userId: {}, scenicId: {}", userId, scenicId, e);
            throw new RuntimeException("根据用户ID和景区ID获取使用记录失败");
        }
    }

    @Override
    public List<CouponUsageRecord> getRecordsByStatus(String usageStatus) {
        try {
            return baseMapper.selectRecordsByStatus(usageStatus);
        } catch (Exception e) {
            log.error("根据使用状态获取记录失败，usageStatus: {}", usageStatus, e);
            throw new RuntimeException("根据使用状态获取记录失败");
        }
    }

    @Override
    @Transactional
    public boolean createBundleUsageRecords(Integer couponId, Integer userId, List<String> scenicIds) {
        try {
            if (scenicIds == null || scenicIds.isEmpty()) {
                log.warn("景区ID列表为空，无法创建使用记录");
                return false;
            }

            List<CouponUsageRecord> records = new ArrayList<>();
            for (String scenicId : scenicIds) {
                CouponUsageRecord record = new CouponUsageRecord();
                record.setCouponId(couponId);
                record.setScenicId(scenicId);
                record.setUserId(userId);
                record.setUsageStatus("unused");
                records.add(record);
            }

            boolean success = saveBatch(records);
            if (success) {
                log.info("组合包卡券使用记录创建成功，couponId: {}, 景区数量: {}", couponId, scenicIds.size());
            } else {
                log.error("组合包卡券使用记录创建失败，couponId: {}", couponId);
            }
            return success;
        } catch (Exception e) {
            log.error("创建组合包卡券使用记录失败，couponId: {}", couponId, e);
            throw new RuntimeException("创建组合包卡券使用记录失败");
        }
    }

    @Override
    @Transactional
    public boolean markAsUsed(Integer couponId, String scenicId) {
        try {
            int updatedRows = baseMapper.updateUsageStatusToUsed(couponId, scenicId);
            boolean success = updatedRows > 0;
            if (success) {
                log.info("卡券使用记录更新成功，couponId: {}, scenicId: {}", couponId, scenicId);
            } else {
                log.warn("卡券使用记录更新失败，可能记录不存在或已使用，couponId: {}, scenicId: {}", couponId, scenicId);
            }
            return success;
        } catch (Exception e) {
            log.error("标记卡券已使用失败，couponId: {}, scenicId: {}", couponId, scenicId, e);
            throw new RuntimeException("标记卡券已使用失败");
        }
    }

    @Override
    @Transactional
    public boolean batchCreateRecords(List<CouponUsageRecord> records) {
        try {
            if (records == null || records.isEmpty()) {
                log.warn("使用记录列表为空，无法批量创建");
                return false;
            }

            boolean success = saveBatch(records);
            if (success) {
                log.info("批量创建使用记录成功，记录数量: {}", records.size());
            } else {
                log.error("批量创建使用记录失败");
            }
            return success;
        } catch (Exception e) {
            log.error("批量创建使用记录失败", e);
            throw new RuntimeException("批量创建使用记录失败");
        }
    }

    @Override
    public List<CouponUsageRecord> getUsageStatsByCouponId(Integer couponId) {
        try {
            return baseMapper.selectUsageStatsByCouponId(couponId);
        } catch (Exception e) {
            log.error("获取卡券使用统计失败，couponId: {}", couponId, e);
            throw new RuntimeException("获取卡券使用统计失败");
        }
    }

    @Override
    public boolean isUsedInScenic(Integer couponId, String scenicId) {
        try {
            CouponUsageRecord record = getRecordByCouponAndScenic(couponId, scenicId);
            return record != null && "used".equals(record.getUsageStatus());
        } catch (Exception e) {
            log.error("检查卡券在景区使用状态失败，couponId: {}, scenicId: {}", couponId, scenicId, e);
            return false;
        }
    }

    @Override
    public List<String> getUnusedScenicIds(Integer couponId) {
        try {
            List<CouponUsageRecord> records = getRecordsByCouponId(couponId);
            return records.stream()
                    .filter(record -> "unused".equals(record.getUsageStatus()))
                    .map(CouponUsageRecord::getScenicId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取卡券未使用景区列表失败，couponId: {}", couponId, e);
            throw new RuntimeException("获取卡券未使用景区列表失败");
        }
    }

    @Override
    public List<String> getUsedScenicIds(Integer couponId) {
        try {
            List<CouponUsageRecord> records = getRecordsByCouponId(couponId);
            return records.stream()
                    .filter(record -> "used".equals(record.getUsageStatus()))
                    .map(CouponUsageRecord::getScenicId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取卡券已使用景区列表失败，couponId: {}", couponId, e);
            throw new RuntimeException("获取卡券已使用景区列表失败");
        }
    }
}
